<template>
  <view class="online-exam-container">
    <!-- 考前准备阶段 -->
    <view v-if="examStage === 'preparation'" class="preparation-stage">
      <view class="exam-info">
        <text class="exam-title">{{ examInfo.name }}</text>
        <view class="exam-details">
          <view class="detail-item">
            <u-icon name="clock" size="16" />
            <text>考试时长：{{ examInfo.duration }}分钟</text>
          </view>
          <view class="detail-item">
            <u-icon name="file-text" size="16" />
            <text>题目数量：{{ examInfo.totalQuestions }}题</text>
          </view>
          <view class="detail-item">
            <u-icon name="award" size="16" />
            <text>及格分数：{{ examInfo.passScore }}分</text>
          </view>
        </view>
      </view>

      <!-- 考试须知 -->
      <view class="exam-rules">
        <text class="rules-title">考试须知</text>
        <view class="rules-content">
          <view class="rule-item">
            <text class="rule-icon">1.</text>
            <text class="rule-text">考试过程中需要开启摄像头进行人脸识别监控</text>
          </view>
          <view class="rule-item">
            <text class="rule-icon">2.</text>
            <text class="rule-text">考试期间不得切换应用或退出考试界面</text>
          </view>
          <view class="rule-item">
            <text class="rule-icon">3.</text>
            <text class="rule-text">每题答题时间有限制，请合理安排时间</text>
          </view>
          <view class="rule-item">
            <text class="rule-icon">4.</text>
            <text class="rule-text">考试提交后不可修改，请仔细检查后提交</text>
          </view>
          <view class="rule-item">
            <text class="rule-icon">5.</text>
            <text class="rule-text">如遇技术问题，请及时联系监考老师</text>
          </view>
        </view>
      </view>

      <!-- 设备检测 -->
      <view class="device-check">
        <text class="check-title">设备检测</text>
        <view class="check-items">
          <view class="check-item" :class="{ passed: cameraPermission }">
            <u-icon :name="cameraPermission ? 'checkmark-circle' : 'close-circle'" 
                   :color="cameraPermission ? '#4caf50' : '#f44336'" size="20" />
            <text class="check-text">摄像头权限</text>
            <button v-if="!cameraPermission" class="check-btn" @tap="requestCameraPermission">
              授权
            </button>
          </view>
          
          <view class="check-item" :class="{ passed: networkStatus }">
            <u-icon :name="networkStatus ? 'checkmark-circle' : 'close-circle'" 
                   :color="networkStatus ? '#4caf50' : '#f44336'" size="20" />
            <text class="check-text">网络连接</text>
          </view>
        </view>
      </view>

      <!-- 开始考试按钮 -->
      <view class="start-section">
        <button 
          class="start-btn"
          :disabled="!canStartExam"
          @tap="startExam"
        >
          {{ canStartExam ? '开始考试' : '请完成设备检测' }}
        </button>
      </view>
    </view>

    <!-- 人脸识别阶段 -->
    <view v-else-if="examStage === 'faceVerification'" class="face-verification-stage">
      <view class="verification-header">
        <text class="verification-title">人脸识别验证</text>
        <text class="verification-desc">请将面部对准摄像头，保持光线充足</text>
      </view>

      <!-- 摄像头预览区域 -->
      <view class="camera-preview">
        <camera 
          class="camera"
          device-position="front"
          flash="off"
          @initdone="onCameraReady"
          @error="onCameraError"
        />
        
        <view class="face-guide">
          <view class="guide-circle"></view>
          <text class="guide-text">请将面部置于圆圈内</text>
        </view>
      </view>

      <!-- 验证状态 -->
      <view class="verification-status">
        <view v-if="faceVerifying" class="verifying-state">
          <u-loading-icon mode="spinner" />
          <text class="status-text">正在验证身份...</text>
        </view>
        
        <view v-else class="verification-actions">
          <button class="capture-btn" @tap="captureFace">
            拍照验证
          </button>
        </view>
      </view>
    </view>

    <!-- 考试进行阶段 -->
    <view v-else-if="examStage === 'examination'" class="examination-stage">
      <!-- 考试头部 -->
      <view class="exam-header">
        <view class="exam-progress">
          <text class="progress-text">{{ currentQuestionIndex + 1 }}/{{ examQuestions.length }}</text>
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              :style="{ width: progressPercent + '%' }"
            ></view>
          </view>
        </view>
        
        <view class="exam-timer">
          <u-icon name="clock" size="16" color="#ff9800" />
          <text class="timer-text">{{ formatTime(remainingTime) }}</text>
        </view>
      </view>

      <!-- 题目内容 -->
      <view class="question-section">
        <view v-if="currentQuestion" class="question-content">
          <view class="question-header">
            <text class="question-type">{{ getQuestionTypeText(currentQuestion.type) }}</text>
            <text class="question-score">{{ currentQuestion.score }}分</text>
          </view>
          
          <text class="question-stem">{{ currentQuestion.stem }}</text>
          
          <!-- 题目图片 -->
          <image 
            v-if="currentQuestion.image" 
            class="question-image"
            :src="currentQuestion.image"
            mode="widthFix"
            @tap="previewImage(currentQuestion.image)"
          />
          
          <!-- 选项列表 -->
          <view class="options-list">
            <view 
              v-for="(option, index) in currentQuestion.options" 
              :key="index"
              class="option-item"
              :class="{ selected: selectedAnswers.includes(option.key) }"
              @tap="selectOption(option.key)"
            >
              <view class="option-key">{{ option.key }}</view>
              <text class="option-text">{{ option.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 答题操作 -->
      <view class="answer-actions">
        <button 
          v-if="currentQuestionIndex > 0"
          class="prev-btn"
          @tap="prevQuestion"
        >
          上一题
        </button>
        
        <button 
          v-if="currentQuestionIndex < examQuestions.length - 1"
          class="next-btn"
          :disabled="selectedAnswers.length === 0"
          @tap="nextQuestion"
        >
          下一题
        </button>
        
        <button 
          v-else
          class="submit-btn"
          @tap="showSubmitConfirm"
        >
          提交试卷
        </button>
      </view>
    </view>

    <!-- 提交确认弹窗 -->
    <u-modal
      v-model="showSubmitModal"
      title="确认提交"
      :content="submitModalContent"
      show-cancel-button
      @confirm="submitExam"
      @cancel="showSubmitModal = false"
    />

    <!-- 人脸识别失败弹窗 -->
    <u-modal
      v-model="showFaceFailModal"
      title="身份验证失败"
      content="人脸识别验证失败，请重新验证或联系监考老师"
      show-cancel-button
      confirm-text="重新验证"
      cancel-text="退出考试"
      @confirm="retryFaceVerification"
      @cancel="exitExam"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { getExamDetail, getExamQuestions, submitExamAnswers } from '../../../../src/api/modules/exam';

// 页面参数
const props = defineProps<{
  id: string;
}>();

// 响应式数据
const examStage = ref<'preparation' | 'faceVerification' | 'examination' | 'completed'>('preparation');
const examInfo = ref<any>({});
const examQuestions = ref<any[]>([]);
const currentQuestionIndex = ref(0);
const selectedAnswers = ref<string[]>([]);
const allAnswers = ref<Record<number, string[]>>({});
const remainingTime = ref(0);
const cameraPermission = ref(false);
const networkStatus = ref(true);
const faceVerifying = ref(false);
const showSubmitModal = ref(false);
const showFaceFailModal = ref(false);

// 计算属性
const currentQuestion = computed(() => examQuestions.value[currentQuestionIndex.value]);
const progressPercent = computed(() => 
  examQuestions.value.length > 0 ? ((currentQuestionIndex.value + 1) / examQuestions.value.length) * 100 : 0
);
const canStartExam = computed(() => cameraPermission.value && networkStatus.value);
const submitModalContent = computed(() => {
  const unansweredCount = examQuestions.value.length - Object.keys(allAnswers.value).length;
  if (unansweredCount > 0) {
    return `还有${unansweredCount}题未作答，确定要提交吗？`;
  }
  return '确定要提交试卷吗？提交后不可修改。';
});

// 定时器
let examTimer: NodeJS.Timeout | null = null;

onMounted(() => {
  loadExamInfo();
  checkNetworkStatus();
});

onUnmounted(() => {
  if (examTimer) {
    clearInterval(examTimer);
  }
});

/**
 * 加载考试信息
 */
async function loadExamInfo() {
  try {
    examInfo.value = await getExamDetail(props.id);
    remainingTime.value = examInfo.value.duration * 60; // 转换为秒
  } catch (error) {
    console.error('加载考试信息失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 检查网络状态
 */
function checkNetworkStatus() {
  uni.getNetworkType({
    success: (res) => {
      networkStatus.value = res.networkType !== 'none';
    }
  });
}

/**
 * 请求摄像头权限
 */
function requestCameraPermission() {
  uni.authorize({
    scope: 'scope.camera',
    success: () => {
      cameraPermission.value = true;
    },
    fail: () => {
      uni.showModal({
        title: '需要摄像头权限',
        content: '考试需要使用摄像头进行身份验证，请在设置中开启权限',
        showCancel: false
      });
    }
  });
}

/**
 * 开始考试
 */
function startExam() {
  if (!canStartExam.value) return;
  examStage.value = 'faceVerification';
}

/**
 * 摄像头准备就绪
 */
function onCameraReady() {
  console.log('摄像头准备就绪');
}

/**
 * 摄像头错误
 */
function onCameraError(error: any) {
  console.error('摄像头错误:', error);
  uni.showToast({
    title: '摄像头启动失败',
    icon: 'none'
  });
}

/**
 * 拍照进行人脸识别
 */
function captureFace() {
  faceVerifying.value = true;

  // 模拟人脸识别过程
  setTimeout(() => {
    const success = Math.random() > 0.1; // 90%成功率

    if (success) {
      // 验证成功，开始考试
      startExamination();
    } else {
      // 验证失败
      showFaceFailModal.value = true;
    }

    faceVerifying.value = false;
  }, 3000);
}

/**
 * 重新进行人脸验证
 */
function retryFaceVerification() {
  showFaceFailModal.value = false;
  // 重置验证状态
}

/**
 * 退出考试
 */
function exitExam() {
  uni.navigateBack();
}

/**
 * 开始正式考试
 */
async function startExamination() {
  try {
    // 加载考试题目
    examQuestions.value = await getExamQuestions(props.id);

    // 切换到考试阶段
    examStage.value = 'examination';

    // 开始计时
    startExamTimer();

    // 监听页面切换（防作弊）
    uni.onAppHide(() => {
      handleAppHide();
    });

  } catch (error) {
    console.error('加载考试题目失败:', error);
    uni.showToast({
      title: '加载题目失败',
      icon: 'none'
    });
  }
}

/**
 * 开始考试计时
 */
function startExamTimer() {
  examTimer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      // 时间到，自动提交
      submitExam();
    }
  }, 1000);
}

/**
 * 处理应用切换到后台（防作弊检测）
 */
function handleAppHide() {
  uni.showModal({
    title: '考试异常',
    content: '检测到您切换了应用，这可能影响考试成绩。请保持在考试界面。',
    showCancel: false
  });
}

/**
 * 选择选项
 */
function selectOption(optionKey: string) {
  const question = currentQuestion.value;

  if (question.type === 'single') {
    selectedAnswers.value = [optionKey];
  } else if (question.type === 'multiple') {
    const index = selectedAnswers.value.indexOf(optionKey);
    if (index > -1) {
      selectedAnswers.value.splice(index, 1);
    } else {
      selectedAnswers.value.push(optionKey);
    }
  }
}

/**
 * 上一题
 */
function prevQuestion() {
  // 保存当前题目答案
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  // 切换到上一题
  currentQuestionIndex.value--;
  loadQuestionAnswers();
}

/**
 * 下一题
 */
function nextQuestion() {
  // 保存当前题目答案
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  // 切换到下一题
  currentQuestionIndex.value++;
  loadQuestionAnswers();
}

/**
 * 加载题目已选答案
 */
function loadQuestionAnswers() {
  const savedAnswers = allAnswers.value[currentQuestionIndex.value];
  selectedAnswers.value = savedAnswers ? [...savedAnswers] : [];
}

/**
 * 显示提交确认
 */
function showSubmitConfirm() {
  // 保存当前题目答案
  if (selectedAnswers.value.length > 0) {
    allAnswers.value[currentQuestionIndex.value] = [...selectedAnswers.value];
  }

  showSubmitModal.value = true;
}

/**
 * 提交考试
 */
async function submitExam() {
  try {
    showSubmitModal.value = false;

    if (examTimer) {
      clearInterval(examTimer);
    }

    uni.showLoading({ title: '提交中...' });

    // 提交答案
    const result = await submitExamAnswers({
      examId: props.id,
      answers: allAnswers.value,
      usedTime: (examInfo.value.duration * 60) - remainingTime.value
    });

    uni.hideLoading();

    // 跳转到结果页面
    uni.redirectTo({
      url: `/subpackages/exam/pages/exam-result/exam-result?examId=${props.id}&score=${result.score}&passed=${result.passed}`
    });

  } catch (error) {
    uni.hideLoading();
    console.error('提交考试失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 预览图片
 */
function previewImage(imageUrl: string) {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
}

/**
 * 获取题目类型文本
 */
function getQuestionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题'
  };
  return typeMap[type] || type;
}

/**
 * 格式化时间
 */
function formatTime(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.online-exam-container {
  min-height: 100vh;
  background-color: $background-color;
}

// 考前准备阶段样式
.preparation-stage {
  padding: $spacing-lg;

  .exam-info {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;
    box-shadow: $shadow-medium;

    .exam-title {
      display: block;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-lg;
      text-align: center;
    }

    .exam-details {
      .detail-item {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        margin-bottom: $spacing-md;
        font-size: $font-size-md;
        color: $text-secondary;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .exam-rules {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;

    .rules-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-lg;
    }

    .rules-content {
      .rule-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: $spacing-md;

        .rule-icon {
          color: $primary-color;
          font-weight: bold;
          margin-right: $spacing-sm;
          margin-top: 2rpx;
          min-width: 40rpx;
        }

        .rule-text {
          flex: 1;
          font-size: $font-size-sm;
          color: $text-secondary;
          line-height: 1.5;
        }
      }
    }
  }

  .device-check {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;

    .check-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-lg;
    }

    .check-items {
      .check-item {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        padding: $spacing-md;
        border-radius: $border-radius-medium;
        margin-bottom: $spacing-sm;
        background-color: $background-color;

        &.passed {
          background-color: $success-light;
        }

        .check-text {
          flex: 1;
          font-size: $font-size-md;
          color: $text-primary;
        }

        .check-btn {
          background-color: $primary-color;
          color: white;
          border: none;
          border-radius: $border-radius-small;
          padding: $spacing-xs $spacing-sm;
          font-size: $font-size-sm;
        }
      }
    }
  }

  .start-section {
    .start-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(135deg, $primary-color, $primary-dark);
      color: white;
      border: none;
      border-radius: $border-radius-large;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;

      &:disabled {
        background: $text-disabled;
        opacity: 0.6;
      }
    }
  }
}

// 人脸识别阶段样式
.face-verification-stage {
  padding: $spacing-lg;

  .verification-header {
    text-align: center;
    margin-bottom: $spacing-xl;

    .verification-title {
      display: block;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }

    .verification-desc {
      font-size: $font-size-md;
      color: $text-secondary;
    }
  }

  .camera-preview {
    position: relative;
    height: 600rpx;
    border-radius: $border-radius-large;
    overflow: hidden;
    margin-bottom: $spacing-xl;

    .camera {
      width: 100%;
      height: 100%;
    }

    .face-guide {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;

      .guide-circle {
        width: 400rpx;
        height: 400rpx;
        border: 4rpx solid rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        margin-bottom: $spacing-md;
      }

      .guide-text {
        color: white;
        font-size: $font-size-sm;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
      }
    }
  }

  .verification-status {
    .verifying-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-md;

      .status-text {
        font-size: $font-size-md;
        color: $text-secondary;
      }
    }

    .verification-actions {
      text-align: center;

      .capture-btn {
        width: 200rpx;
        height: 200rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, $primary-color, $primary-dark);
        color: white;
        border: none;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
      }
    }
  }
}

// 考试进行阶段样式
.examination-stage {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .exam-header {
    background-color: $surface-color;
    padding: $spacing-md $spacing-lg;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1rpx solid $divider-color;

    .exam-progress {
      flex: 1;

      .progress-text {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        margin-bottom: $spacing-xs;
      }

      .progress-bar {
        height: 6rpx;
        background-color: $divider-color;
        border-radius: 3rpx;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary-color, $primary-light);
          transition: width 0.3s ease;
        }
      }
    }

    .exam-timer {
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .timer-text {
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $warning-color;
      }
    }
  }

  .question-section {
    flex: 1;
    padding: $spacing-lg;

    .question-content {
      .question-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: $spacing-lg;

        .question-type {
          background-color: $primary-color;
          color: white;
          font-size: $font-size-xs;
          padding: 4rpx 12rpx;
          border-radius: $border-radius-small;
        }

        .question-score {
          font-size: $font-size-sm;
          color: $text-secondary;
        }
      }

      .question-stem {
        display: block;
        font-size: $font-size-lg;
        line-height: 1.6;
        color: $text-primary;
        margin-bottom: $spacing-lg;
        background-color: $surface-color;
        padding: $spacing-xl;
        border-radius: $border-radius-large;
      }

      .question-image {
        width: 100%;
        border-radius: $border-radius-medium;
        margin: $spacing-md 0;
      }

      .options-list {
        .option-item {
          display: flex;
          align-items: flex-start;
          background-color: $surface-color;
          border: 2rpx solid transparent;
          border-radius: $border-radius-medium;
          padding: $spacing-lg;
          margin-bottom: $spacing-md;
          transition: all 0.2s ease;

          &.selected {
            border-color: $primary-color;
            background-color: $primary-light;
          }

          .option-key {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            background-color: $background-color;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: $font-size-md;
            font-weight: $font-weight-medium;
            color: $text-primary;
            margin-right: $spacing-md;
            flex-shrink: 0;
          }

          .option-text {
            flex: 1;
            font-size: $font-size-md;
            line-height: 1.5;
            color: $text-primary;
          }
        }
      }
    }
  }

  .answer-actions {
    display: flex;
    gap: $spacing-md;
    padding: $spacing-lg;
    border-top: 1rpx solid $divider-color;
    background-color: $surface-color;

    .prev-btn, .next-btn, .submit-btn {
      flex: 1;
      height: 88rpx;
      border: none;
      border-radius: $border-radius-large;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
    }

    .prev-btn {
      background-color: $text-disabled;
      color: white;
    }

    .next-btn {
      background: linear-gradient(135deg, $primary-color, $primary-dark);
      color: white;

      &:disabled {
        background: $text-disabled;
        opacity: 0.6;
      }
    }

    .submit-btn {
      background: linear-gradient(135deg, $success-color, $success-dark);
      color: white;
    }
  }
}
</style>
