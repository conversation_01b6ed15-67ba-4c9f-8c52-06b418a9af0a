<template>
  <view class="history-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">考试记录</text>
      <text class="page-desc">查看历史考试记录</text>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tabs">
          <view 
            v-for="filter in filterOptions" 
            :key="filter.value"
            class="filter-tab"
            :class="{ active: selectedFilter === filter.value }"
            @tap="selectFilter(filter.value)"
          >
            {{ filter.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalExams }}</text>
          <text class="stats-label">总考试次数</text>
        </view>
        
        <view class="stats-item">
          <text class="stats-number">{{ passedExams }}</text>
          <text class="stats-label">通过次数</text>
        </view>
        
        <view class="stats-item">
          <text class="stats-number">{{ passRate }}%</text>
          <text class="stats-label">通过率</text>
        </view>
      </view>
    </view>

    <!-- 考试记录列表 -->
    <view class="history-list">
      <view v-if="loading" class="loading-state">
        <u-loading-icon mode="spinner" />
        <text class="loading-text">加载中...</text>
      </view>

      <view v-else-if="historyList.length === 0" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无考试记录</text>
      </view>

      <view v-else>
        <view 
          v-for="record in historyList" 
          :key="record.id"
          class="history-item"
          @tap="viewDetail(record)"
        >
          <view class="item-header">
            <text class="exam-name">{{ record.examName }}</text>
            <view class="exam-type" :class="record.type">
              {{ record.type === 'online' ? '线上' : '线下' }}
            </view>
          </view>
          
          <view class="item-content">
            <view class="content-left">
              <view class="exam-info">
                <text class="exam-time">{{ formatDateTime(record.examTime) }}</text>
                <text class="exam-duration">用时：{{ formatDuration(record.usedTime) }}</text>
              </view>
              
              <view class="exam-details">
                <text class="detail-text">{{ record.totalQuestions }}题</text>
                <text class="detail-text">及格线：{{ record.passScore }}分</text>
              </view>
            </view>
            
            <view class="content-right">
              <view class="score-section">
                <text class="score-number" :class="record.status">{{ record.score || '--' }}</text>
                <text class="score-unit">分</text>
              </view>
              
              <view class="status-badge" :class="record.status">
                {{ getStatusText(record.status) }}
              </view>
            </view>
          </view>
          
          <!-- 成绩分析 -->
          <view v-if="record.status === 'completed'" class="item-analysis">
            <view class="analysis-item">
              <text class="analysis-label">正确率：</text>
              <text class="analysis-value">{{ record.correctRate }}%</text>
            </view>
            
            <view class="analysis-item">
              <text class="analysis-label">答对题数：</text>
              <text class="analysis-value">{{ record.correctCount }}/{{ record.totalQuestions }}</text>
            </view>
          </view>
          
          <view class="item-actions">
            <button 
              v-if="record.status === 'completed'"
              class="action-btn view-btn"
              @tap.stop="viewAnswers(record)"
            >
              查看答案
            </button>
            
            <button 
              v-if="record.certificate"
              class="action-btn cert-btn"
              @tap.stop="viewCertificate(record)"
            >
              查看证书
            </button>
            
            <button 
              v-if="record.canRetake"
              class="action-btn retake-btn"
              @tap.stop="retakeExam(record)"
            >
              重新考试
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more" @tap="loadMore">
      <text class="load-more-text">点击加载更多</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getExamHistory } from '../../../../src/api/modules/exam';

// 响应式数据
const historyList = ref<any[]>([]);
const loading = ref(false);
const selectedFilter = ref('all');
const currentPage = ref(1);
const hasMore = ref(true);
const pageSize = 10;

// 筛选选项
const filterOptions = ref([
  { label: '全部', value: 'all' },
  { label: '已完成', value: 'completed' },
  { label: '已通过', value: 'passed' },
  { label: '未通过', value: 'failed' },
  { label: '进行中', value: 'in_progress' },
  { label: '线上考试', value: 'online' },
  { label: '线下考试', value: 'offline' }
]);

// 计算属性
const totalExams = computed(() => historyList.value.length);
const passedExams = computed(() => 
  historyList.value.filter(record => record.status === 'passed').length
);
const passRate = computed(() => 
  totalExams.value > 0 ? Math.round((passedExams.value / totalExams.value) * 100) : 0
);

onMounted(() => {
  loadHistoryList();
});

/**
 * 加载考试记录
 */
async function loadHistoryList(isLoadMore = false) {
  if (loading.value) return;
  
  try {
    loading.value = true;
    
    const params = {
      page: isLoadMore ? currentPage.value : 1,
      pageSize,
      filter: selectedFilter.value === 'all' ? undefined : selectedFilter.value
    };
    
    const result = await getExamHistory(params);
    
    if (isLoadMore) {
      historyList.value.push(...result.list);
    } else {
      historyList.value = result.list;
      currentPage.value = 1;
    }
    
    hasMore.value = result.list.length === pageSize;
    
  } catch (error) {
    console.error('加载考试记录失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 选择筛选条件
 */
function selectFilter(filterValue: string) {
  selectedFilter.value = filterValue;
  currentPage.value = 1;
  loadHistoryList();
}

/**
 * 加载更多
 */
function loadMore() {
  currentPage.value++;
  loadHistoryList(true);
}

/**
 * 查看详情
 */
function viewDetail(record: any) {
  uni.navigateTo({
    url: `/subpackages/exam/pages/exam-detail/exam-detail?id=${record.id}`
  });
}

/**
 * 查看答案
 */
function viewAnswers(record: any) {
  uni.navigateTo({
    url: `/subpackages/exam/pages/exam-answers/exam-answers?examId=${record.examId}&recordId=${record.id}`
  });
}

/**
 * 查看证书
 */
function viewCertificate(record: any) {
  uni.navigateTo({
    url: `/subpackages/profile/pages/certificates/certificates?certificateId=${record.certificate.id}`
  });
}

/**
 * 重新考试
 */
function retakeExam(record: any) {
  uni.showModal({
    title: '重新考试',
    content: `确定要重新参加"${record.examName}"考试吗？`,
    success: (res) => {
      if (res.confirm) {
        if (record.type === 'online') {
          uni.navigateTo({
            url: `/subpackages/exam/pages/online-exam/online-exam?id=${record.examId}`
          });
        } else {
          uni.navigateTo({
            url: `/subpackages/exam/pages/offline-exam/offline-exam?id=${record.examId}`
          });
        }
      }
    }
  });
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    completed: '已完成',
    passed: '已通过',
    failed: '未通过',
    in_progress: '进行中',
    not_started: '未开始',
    expired: '已过期'
  };
  return statusMap[status] || status;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr: string) {
  const date = new Date(dateTimeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天 ' + date.toLocaleTimeString().slice(0, 5);
  } else if (days === 1) {
    return '昨天 ' + date.toLocaleTimeString().slice(0, 5);
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
}

/**
 * 格式化时长
 */
function formatDuration(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.history-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $info-color, $info-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.filter-section {
  background-color: white;
  border-bottom: 1rpx solid $divider-color;

  .filter-scroll {
    white-space: nowrap;

    .filter-tabs {
      display: flex;
      padding: $spacing-sm $spacing-md;

      .filter-tab {
        flex-shrink: 0;
        padding: $spacing-sm $spacing-md;
        margin-right: $spacing-sm;
        border-radius: $border-radius-medium;
        font-size: $font-size-sm;
        color: $text-secondary;
        background-color: $background-color;

        &.active {
          background-color: $info-color;
          color: white;
        }
      }
    }
  }
}

.stats-section {
  padding: $spacing-lg;

  .stats-card {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    display: flex;
    justify-content: space-around;
    box-shadow: $shadow-light;

    .stats-item {
      text-align: center;

      .stats-number {
        display: block;
        font-size: $font-size-xxl;
        font-weight: $font-weight-bold;
        color: $info-color;
        margin-bottom: $spacing-xs;
      }

      .stats-label {
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }
  }
}

.history-list {
  padding: 0 $spacing-lg $spacing-lg;

  .loading-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;

    .loading-text, .empty-text {
      margin-top: $spacing-md;
      font-size: $font-size-md;
      color: $text-secondary;
    }

    .empty-icon {
      font-size: 120rpx;
      opacity: 0.5;
    }
  }

  .history-item {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow-light;

    .item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-md;

      .exam-name {
        flex: 1;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-right: $spacing-sm;
      }

      .exam-type {
        padding: 4rpx 8rpx;
        border-radius: $border-radius-small;
        font-size: $font-size-xs;
        color: white;

        &.online {
          background-color: $primary-color;
        }

        &.offline {
          background-color: $secondary-color;
        }
      }
    }

    .item-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: $spacing-md;

      .content-left {
        flex: 1;

        .exam-info {
          margin-bottom: $spacing-sm;

          .exam-time {
            display: block;
            font-size: $font-size-md;
            color: $text-primary;
            margin-bottom: $spacing-xs;
          }

          .exam-duration {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }

        .exam-details {
          display: flex;
          gap: $spacing-md;

          .detail-text {
            font-size: $font-size-xs;
            color: $text-disabled;
          }
        }
      }

      .content-right {
        text-align: right;

        .score-section {
          margin-bottom: $spacing-sm;

          .score-number {
            font-size: $font-size-xxl;
            font-weight: $font-weight-bold;

            &.passed {
              color: $success-color;
            }

            &.failed {
              color: $error-color;
            }

            &.completed {
              color: $text-primary;
            }
          }

          .score-unit {
            font-size: $font-size-sm;
            color: $text-secondary;
            margin-left: $spacing-xs;
          }
        }

        .status-badge {
          font-size: $font-size-xs;
          padding: 2rpx 8rpx;
          border-radius: $border-radius-small;

          &.passed {
            background-color: $success-light;
            color: $success-color;
          }

          &.failed {
            background-color: $error-light;
            color: $error-color;
          }

          &.completed {
            background-color: $info-light;
            color: $info-color;
          }

          &.in_progress {
            background-color: $warning-light;
            color: $warning-color;
          }
        }
      }
    }

    .item-analysis {
      display: flex;
      gap: $spacing-lg;
      margin-bottom: $spacing-md;
      padding: $spacing-sm;
      background-color: $background-color;
      border-radius: $border-radius-medium;

      .analysis-item {
        display: flex;
        align-items: center;

        .analysis-label {
          font-size: $font-size-xs;
          color: $text-secondary;
          margin-right: $spacing-xs;
        }

        .analysis-value {
          font-size: $font-size-sm;
          color: $text-primary;
          font-weight: $font-weight-medium;
        }
      }
    }

    .item-actions {
      display: flex;
      gap: $spacing-sm;

      .action-btn {
        flex: 1;
        height: 60rpx;
        border: none;
        border-radius: $border-radius-medium;
        font-size: $font-size-xs;

        &.view-btn {
          background-color: $info-light;
          color: $info-color;
        }

        &.cert-btn {
          background-color: $warning-light;
          color: $warning-color;
        }

        &.retake-btn {
          background-color: $primary-light;
          color: $primary-color;
        }
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: $spacing-lg;

  .load-more-text {
    font-size: $font-size-sm;
    color: $info-color;
  }
}
</style>
