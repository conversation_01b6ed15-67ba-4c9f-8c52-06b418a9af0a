<template>
  <view class="feedback-container">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">投诉与建议</text>
      <text class="page-desc">您的意见是我们改进的动力</text>
    </view>

    <!-- 反馈表单 -->
    <view class="feedback-form">
      <view class="form-section">
        <text class="section-title">反馈类型</text>
        
        <view class="type-options">
          <view 
            v-for="type in feedbackTypes" 
            :key="type.value"
            class="type-option"
            :class="{ active: selectedType === type.value }"
            @tap="selectType(type.value)"
          >
            <view class="option-icon">{{ type.icon }}</view>
            <text class="option-text">{{ type.label }}</text>
          </view>
        </view>
      </view>

      <view class="form-section">
        <text class="section-title">问题描述</text>
        
        <u-textarea
          v-model="feedbackContent"
          placeholder="请详细描述您遇到的问题或建议..."
          :maxlength="500"
          count
          height="200"
          :border="true"
          :custom-style="textareaStyle"
        />
      </view>

      <view class="form-section">
        <text class="section-title">联系方式（选填）</text>
        
        <u-input
          v-model="contactInfo"
          placeholder="请输入您的联系方式，便于我们回复"
          :border="true"
          :custom-style="inputStyle"
        />
      </view>

      <view class="form-section">
        <text class="section-title">相关截图（选填）</text>
        
        <view class="image-upload">
          <view 
            v-for="(image, index) in uploadedImages" 
            :key="index"
            class="image-item"
          >
            <image 
              class="uploaded-image"
              :src="image.url"
              mode="aspectFill"
              @tap="previewImage(image.url, index)"
            />
            <view class="image-delete" @tap="deleteImage(index)">
              <u-icon name="close" size="12" color="#fff" />
            </view>
          </view>
          
          <view 
            v-if="uploadedImages.length < 3"
            class="upload-btn"
            @tap="chooseImage"
          >
            <u-icon name="plus" size="24" color="#999" />
            <text class="upload-text">添加图片</text>
          </view>
        </view>
        
        <text class="upload-tip">最多可上传3张图片，每张不超过2MB</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn"
        :disabled="!canSubmit"
        @tap="submitFeedback"
      >
        提交反馈
      </button>
    </view>

    <!-- 历史反馈 -->
    <view class="history-section">
      <view class="history-header" @tap="toggleHistory">
        <text class="history-title">我的反馈记录</text>
        <u-icon 
          :name="showHistory ? 'arrow-up' : 'arrow-down'" 
          size="16" 
          color="#666" 
        />
      </view>
      
      <view v-if="showHistory" class="history-list">
        <view v-if="historyLoading" class="loading-state">
          <u-loading-icon mode="spinner" size="20" />
          <text class="loading-text">加载中...</text>
        </view>
        
        <view v-else-if="feedbackHistory.length === 0" class="empty-state">
          <text class="empty-text">暂无反馈记录</text>
        </view>
        
        <view v-else>
          <view 
            v-for="item in feedbackHistory" 
            :key="item.id"
            class="history-item"
          >
            <view class="item-header">
              <view class="item-type">{{ getFeedbackTypeText(item.type) }}</view>
              <view class="item-status" :class="item.status">
                {{ getStatusText(item.status) }}
              </view>
            </view>
            
            <text class="item-content">{{ item.content }}</text>
            
            <view class="item-footer">
              <text class="item-time">{{ formatTime(item.createdAt) }}</text>
              <text v-if="item.reply" class="item-reply" @tap="viewReply(item)">
                查看回复
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 回复详情弹窗 -->
    <u-modal
      v-model="showReplyModal"
      title="客服回复"
      :show-cancel-button="false"
      confirm-text="知道了"
      @confirm="showReplyModal = false"
    >
      <view v-if="selectedReply" class="reply-content">
        <view class="reply-question">
          <text class="reply-label">您的问题：</text>
          <text class="reply-text">{{ selectedReply.content }}</text>
        </view>
        
        <view class="reply-answer">
          <text class="reply-label">客服回复：</text>
          <text class="reply-text">{{ selectedReply.reply }}</text>
        </view>
        
        <view class="reply-time">
          <text class="reply-time-text">回复时间：{{ formatTime(selectedReply.repliedAt) }}</text>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { submitFeedback as submitFeedbackApi, getFeedbackHistory, uploadFeedbackImage } from '../../../../src/api/modules/feedback';

// 响应式数据
const selectedType = ref('');
const feedbackContent = ref('');
const contactInfo = ref('');
const uploadedImages = ref<any[]>([]);
const showHistory = ref(false);
const feedbackHistory = ref<any[]>([]);
const historyLoading = ref(false);
const showReplyModal = ref(false);
const selectedReply = ref<any>(null);

// 反馈类型
const feedbackTypes = ref([
  { value: 'bug', label: '功能异常', icon: '🐛' },
  { value: 'suggestion', label: '功能建议', icon: '💡' },
  { value: 'complaint', label: '服务投诉', icon: '😤' },
  { value: 'praise', label: '表扬建议', icon: '👍' },
  { value: 'other', label: '其他问题', icon: '❓' }
]);

// 样式配置
const textareaStyle = {
  backgroundColor: '#f8f9fa',
  borderRadius: '8px'
};

const inputStyle = {
  backgroundColor: '#f8f9fa',
  borderRadius: '8px'
};

// 计算属性
const canSubmit = computed(() => {
  return selectedType.value && feedbackContent.value.trim().length >= 10;
});

onMounted(() => {
  loadFeedbackHistory();
});

/**
 * 选择反馈类型
 */
function selectType(type: string) {
  selectedType.value = type;
}

/**
 * 选择图片
 */
function chooseImage() {
  uni.chooseImage({
    count: 3 - uploadedImages.value.length,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      for (const tempFilePath of res.tempFilePaths) {
        try {
          // 检查文件大小
          const fileInfo = await uni.getFileInfo({ filePath: tempFilePath });
          if (fileInfo.size > 2 * 1024 * 1024) {
            uni.showToast({
              title: '图片大小不能超过2MB',
              icon: 'none'
            });
            continue;
          }
          
          // 上传图片
          uni.showLoading({ title: '上传中...' });
          const uploadResult = await uploadFeedbackImage(tempFilePath);
          
          uploadedImages.value.push({
            url: uploadResult.url,
            path: tempFilePath
          });
          
          uni.hideLoading();
        } catch (error) {
          uni.hideLoading();
          console.error('上传图片失败:', error);
          uni.showToast({
            title: '上传失败，请重试',
            icon: 'none'
          });
        }
      }
    }
  });
}

/**
 * 预览图片
 */
function previewImage(url: string, index: number) {
  const urls = uploadedImages.value.map(img => img.url);
  uni.previewImage({
    urls,
    current: index
  });
}

/**
 * 删除图片
 */
function deleteImage(index: number) {
  uploadedImages.value.splice(index, 1);
}

/**
 * 提交反馈
 */
async function submitFeedback() {
  if (!canSubmit.value) return;
  
  try {
    uni.showLoading({ title: '提交中...' });
    
    const feedbackData = {
      type: selectedType.value,
      content: feedbackContent.value.trim(),
      contactInfo: contactInfo.value.trim(),
      images: uploadedImages.value.map(img => img.url)
    };
    
    await submitFeedbackApi(feedbackData);
    
    uni.hideLoading();
    uni.showToast({
      title: '提交成功，感谢您的反馈',
      icon: 'success'
    });
    
    // 重置表单
    resetForm();
    
    // 刷新历史记录
    loadFeedbackHistory();
    
  } catch (error) {
    uni.hideLoading();
    console.error('提交反馈失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 重置表单
 */
function resetForm() {
  selectedType.value = '';
  feedbackContent.value = '';
  contactInfo.value = '';
  uploadedImages.value = [];
}

/**
 * 切换历史记录显示
 */
function toggleHistory() {
  showHistory.value = !showHistory.value;
  if (showHistory.value && feedbackHistory.value.length === 0) {
    loadFeedbackHistory();
  }
}

/**
 * 加载反馈历史
 */
async function loadFeedbackHistory() {
  try {
    historyLoading.value = true;
    feedbackHistory.value = await getFeedbackHistory();
  } catch (error) {
    console.error('加载反馈历史失败:', error);
  } finally {
    historyLoading.value = false;
  }
}

/**
 * 查看回复
 */
function viewReply(item: any) {
  selectedReply.value = item;
  showReplyModal.value = true;
}

/**
 * 获取反馈类型文本
 */
function getFeedbackTypeText(type: string) {
  const typeItem = feedbackTypes.value.find(t => t.value === type);
  return typeItem ? typeItem.label : type;
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    replied: '已回复',
    closed: '已关闭'
  };
  return statusMap[status] || status;
}

/**
 * 格式化时间
 */
function formatTime(timeStr: string) {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天 ' + date.toLocaleTimeString().slice(0, 5);
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.feedback-container {
  min-height: 100vh;
  background-color: $background-color;
}

.page-header {
  background: linear-gradient(135deg, $info-color, $info-light);
  padding: $spacing-xl $spacing-lg;
  text-align: center;

  .page-title {
    display: block;
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-xs;
  }

  .page-desc {
    font-size: $font-size-sm;
    color: rgba(255, 255, 255, 0.8);
  }
}

.feedback-form {
  padding: $spacing-lg;

  .form-section {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;
    box-shadow: $shadow-light;

    .section-title {
      display: block;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-lg;
    }

    .type-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
      gap: $spacing-md;

      .type-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: $spacing-lg;
        border: 2rpx solid $divider-color;
        border-radius: $border-radius-medium;
        background-color: $background-color;
        transition: all 0.2s ease;

        &.active {
          border-color: $info-color;
          background-color: $info-light;
        }

        .option-icon {
          font-size: 60rpx;
          margin-bottom: $spacing-sm;
        }

        .option-text {
          font-size: $font-size-sm;
          color: $text-secondary;
          text-align: center;
        }
      }
    }

    .image-upload {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-md;
      margin-bottom: $spacing-sm;

      .image-item {
        position: relative;
        width: 200rpx;
        height: 200rpx;

        .uploaded-image {
          width: 100%;
          height: 100%;
          border-radius: $border-radius-medium;
        }

        .image-delete {
          position: absolute;
          top: -10rpx;
          right: -10rpx;
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          background-color: $error-color;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .upload-btn {
        width: 200rpx;
        height: 200rpx;
        border: 2rpx dashed $divider-color;
        border-radius: $border-radius-medium;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: $background-color;

        .upload-text {
          font-size: $font-size-xs;
          color: $text-disabled;
          margin-top: $spacing-xs;
        }
      }
    }

    .upload-tip {
      font-size: $font-size-xs;
      color: $text-disabled;
    }
  }
}

.submit-section {
  padding: 0 $spacing-lg $spacing-lg;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, $info-color, $info-dark);
    color: white;
    border: none;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;

    &:disabled {
      background: $text-disabled;
      opacity: 0.6;
    }
  }
}

.history-section {
  margin: $spacing-lg;
  background-color: $surface-color;
  border-radius: $border-radius-large;
  overflow: hidden;
  box-shadow: $shadow-light;

  .history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-lg;
    border-bottom: 1rpx solid $divider-color;

    .history-title {
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      color: $text-primary;
    }
  }

  .history-list {
    .loading-state, .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: $spacing-xl;

      .loading-text, .empty-text {
        margin-left: $spacing-sm;
        font-size: $font-size-sm;
        color: $text-secondary;
      }
    }

    .history-item {
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: $spacing-sm;

        .item-type {
          font-size: $font-size-sm;
          color: $text-primary;
          font-weight: $font-weight-medium;
        }

        .item-status {
          font-size: $font-size-xs;
          padding: 2rpx 8rpx;
          border-radius: $border-radius-small;

          &.pending {
            background-color: $warning-light;
            color: $warning-color;
          }

          &.processing {
            background-color: $info-light;
            color: $info-color;
          }

          &.replied {
            background-color: $success-light;
            color: $success-color;
          }

          &.closed {
            background-color: $text-disabled;
            color: white;
          }
        }
      }

      .item-content {
        display: block;
        font-size: $font-size-sm;
        color: $text-secondary;
        line-height: 1.5;
        margin-bottom: $spacing-sm;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .item-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item-time {
          font-size: $font-size-xs;
          color: $text-disabled;
        }

        .item-reply {
          font-size: $font-size-xs;
          color: $info-color;
        }
      }
    }
  }
}

.reply-content {
  padding: $spacing-lg;

  .reply-question, .reply-answer {
    margin-bottom: $spacing-lg;

    .reply-label {
      display: block;
      font-size: $font-size-sm;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }

    .reply-text {
      font-size: $font-size-sm;
      color: $text-secondary;
      line-height: 1.6;
    }
  }

  .reply-time {
    text-align: right;

    .reply-time-text {
      font-size: $font-size-xs;
      color: $text-disabled;
    }
  }
}
</style>
