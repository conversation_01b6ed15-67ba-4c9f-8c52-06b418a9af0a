<template>
  <view class="access-denied-container">
    <!-- 状态图标和标题 -->
    <view class="status-section">
      <view class="status-icon" :class="statusType">
        {{ getStatusIcon() }}
      </view>
      
      <text class="status-title">{{ getStatusTitle() }}</text>
      <text class="status-desc">{{ getStatusDesc() }}</text>
    </view>

    <!-- 详细说明 -->
    <view class="detail-section">
      <view class="detail-card">
        <text class="detail-title">{{ getDetailTitle() }}</text>
        
        <view class="detail-content">
          <view v-if="statusType === 'incomplete'" class="incomplete-tips">
            <text class="tips-title">完善资料需要：</text>
            <view class="tips-list">
              <view class="tips-item">
                <text class="tips-icon">✓</text>
                <text class="tips-text">填写真实姓名和联系方式</text>
              </view>
              <view class="tips-item">
                <text class="tips-icon">✓</text>
                <text class="tips-text">上传身份证号码</text>
              </view>
              <view class="tips-item">
                <text class="tips-icon">✓</text>
                <text class="tips-text">选择隶属机构和职位</text>
              </view>
              <view class="tips-item">
                <text class="tips-icon">✓</text>
                <text class="tips-text">上传本人照片</text>
              </view>
            </view>
          </view>
          
          <view v-else-if="statusType === 'pending'" class="pending-tips">
            <text class="tips-title">审核流程：</text>
            <view class="process-steps">
              <view class="step-item completed">
                <view class="step-number">1</view>
                <text class="step-text">提交资料</text>
              </view>
              <view class="step-item active">
                <view class="step-number">2</view>
                <text class="step-text">机构审核</text>
              </view>
              <view class="step-item">
                <view class="step-number">3</view>
                <text class="step-text">审核通过</text>
              </view>
            </view>
            <text class="process-note">预计审核时间：1-3个工作日</text>
          </view>
          
          <view v-else-if="statusType === 'rejected'" class="rejected-tips">
            <text class="tips-title">审核未通过原因：</text>
            <view class="rejection-reason">
              <text class="reason-text">{{ rejectionReason || '请联系管理员了解详情' }}</text>
            </view>
            <text class="tips-title">修改建议：</text>
            <view class="tips-list">
              <view class="tips-item">
                <text class="tips-icon">•</text>
                <text class="tips-text">确保个人信息真实有效</text>
              </view>
              <view class="tips-item">
                <text class="tips-icon">•</text>
                <text class="tips-text">照片清晰，符合要求</text>
              </view>
              <view class="tips-item">
                <text class="tips-icon">•</text>
                <text class="tips-text">选择正确的机构和职位</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        v-if="statusType === 'incomplete' || statusType === 'rejected'" 
        class="primary-btn"
        @tap="goToRegister"
      >
        {{ statusType === 'incomplete' ? '立即完善资料' : '重新提交资料' }}
      </button>
      
      <button 
        v-if="statusType === 'pending'" 
        class="secondary-btn"
        @tap="refreshStatus"
      >
        刷新审核状态
      </button>
      
      <button class="text-btn" @tap="contactSupport">
        联系客服
      </button>
    </view>

    <!-- 帮助信息 -->
    <view class="help-section">
      <text class="help-title">需要帮助？</text>
      <view class="help-contacts">
        <view class="contact-item" @tap="callPhone">
          <u-icon name="phone" size="16" />
          <text class="contact-text">客服电话：************</text>
        </view>
        <view class="contact-item" @tap="openWechat">
          <u-icon name="weixin" size="16" />
          <text class="contact-text">微信客服：CDC_Service</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '../../../../src/stores/modules/user';
import { storeToRefs } from 'pinia';

const userStore = useUserStore();
const { profile, userStatus } = storeToRefs(userStore);

// 响应式数据
const rejectionReason = ref('');

// 计算属性
const statusType = computed(() => {
  if (userStore.isIncomplete) return 'incomplete';
  if (userStore.isPending) return 'pending';
  if (userStore.isRejected) return 'rejected';
  return 'unknown';
});

onMounted(() => {
  // 获取审核未通过原因
  if (userStore.isRejected && profile.value?.rejectionReason) {
    rejectionReason.value = profile.value.rejectionReason;
  }
});

/**
 * 获取状态图标
 */
function getStatusIcon() {
  const iconMap: Record<string, string> = {
    incomplete: '📝',
    pending: '⏳',
    rejected: '❌',
    unknown: '❓'
  };
  return iconMap[statusType.value] || '❓';
}

/**
 * 获取状态标题
 */
function getStatusTitle() {
  const titleMap: Record<string, string> = {
    incomplete: '请完善个人资料',
    pending: '资料审核中',
    rejected: '审核未通过',
    unknown: '身份认证异常'
  };
  return titleMap[statusType.value] || '身份认证异常';
}

/**
 * 获取状态描述
 */
function getStatusDesc() {
  const descMap: Record<string, string> = {
    incomplete: '完善资料后即可使用全部功能',
    pending: '请耐心等待机构审核',
    rejected: '请根据提示修改资料后重新提交',
    unknown: '请联系客服处理'
  };
  return descMap[statusType.value] || '请联系客服处理';
}

/**
 * 获取详细标题
 */
function getDetailTitle() {
  const titleMap: Record<string, string> = {
    incomplete: '如何完善资料？',
    pending: '审核进度',
    rejected: '审核反馈',
    unknown: '异常说明'
  };
  return titleMap[statusType.value] || '异常说明';
}

/**
 * 跳转到注册页面
 */
function goToRegister() {
  uni.navigateTo({
    url: '/pages/register/register'
  });
}

/**
 * 刷新审核状态
 */
async function refreshStatus() {
  try {
    uni.showLoading({ title: '刷新中...' });
    
    // 重新获取用户信息
    await userStore.refreshProfile();
    
    uni.hideLoading();
    
    if (userStore.isApproved) {
      uni.showToast({
        title: '审核已通过！',
        icon: 'success'
      });
      
      setTimeout(() => {
        uni.switchTab({ url: '/pages/info/info' });
      }, 1500);
    } else {
      uni.showToast({
        title: '审核仍在进行中',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: '刷新失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 联系客服
 */
function contactSupport() {
  uni.showActionSheet({
    itemList: ['拨打客服电话', '复制微信号'],
    success: (res) => {
      if (res.tapIndex === 0) {
        callPhone();
      } else if (res.tapIndex === 1) {
        openWechat();
      }
    }
  });
}

/**
 * 拨打电话
 */
function callPhone() {
  uni.makePhoneCall({
    phoneNumber: '************'
  });
}

/**
 * 打开微信
 */
function openWechat() {
  uni.setClipboardData({
    data: 'CDC_Service',
    success: () => {
      uni.showToast({
        title: '微信号已复制',
        icon: 'success'
      });
    }
  });
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.access-denied-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: $spacing-lg;
}

.status-section {
  text-align: center;
  padding: $spacing-xxl $spacing-lg;

  .status-icon {
    display: block;
    font-size: 160rpx;
    margin-bottom: $spacing-lg;

    &.incomplete {
      filter: hue-rotate(200deg);
    }

    &.pending {
      filter: hue-rotate(40deg);
    }

    &.rejected {
      filter: hue-rotate(0deg);
    }
  }

  .status-title {
    display: block;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  .status-desc {
    font-size: $font-size-md;
    color: $text-secondary;
    line-height: 1.5;
  }
}

.detail-section {
  margin-bottom: $spacing-xl;

  .detail-card {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    box-shadow: $shadow-medium;

    .detail-title {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-lg;
    }

    .incomplete-tips, .rejected-tips {
      .tips-title {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-md;
      }

      .tips-list {
        .tips-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: $spacing-sm;

          .tips-icon {
            color: $success-color;
            font-weight: bold;
            margin-right: $spacing-sm;
            margin-top: 2rpx;
          }

          .tips-text {
            flex: 1;
            font-size: $font-size-sm;
            color: $text-secondary;
            line-height: 1.5;
          }
        }
      }
    }

    .pending-tips {
      .tips-title {
        display: block;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-lg;
      }

      .process-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: $spacing-lg;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 40rpx;
          left: 40rpx;
          right: 40rpx;
          height: 2rpx;
          background-color: $divider-color;
          z-index: 1;
        }

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          position: relative;
          z-index: 2;

          .step-number {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            background-color: $background-color;
            border: 4rpx solid $divider-color;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: $font-size-md;
            font-weight: $font-weight-bold;
            color: $text-disabled;
            margin-bottom: $spacing-sm;
          }

          .step-text {
            font-size: $font-size-xs;
            color: $text-disabled;
            text-align: center;
          }

          &.completed {
            .step-number {
              background-color: $success-color;
              border-color: $success-color;
              color: white;
            }

            .step-text {
              color: $success-color;
            }
          }

          &.active {
            .step-number {
              background-color: $primary-color;
              border-color: $primary-color;
              color: white;
            }

            .step-text {
              color: $primary-color;
            }
          }
        }
      }

      .process-note {
        text-align: center;
        font-size: $font-size-sm;
        color: $text-secondary;
        font-style: italic;
      }
    }

    .rejection-reason {
      background-color: $error-light;
      border-radius: $border-radius-medium;
      padding: $spacing-md;
      margin-bottom: $spacing-lg;

      .reason-text {
        font-size: $font-size-sm;
        color: $error-color;
        line-height: 1.5;
      }
    }
  }
}

.action-section {
  margin-bottom: $spacing-xl;

  .primary-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    color: white;
    border: none;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    margin-bottom: $spacing-md;

    &:active {
      opacity: 0.8;
    }
  }

  .secondary-btn {
    width: 100%;
    height: 88rpx;
    background-color: transparent;
    color: $primary-color;
    border: 2rpx solid $primary-color;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    margin-bottom: $spacing-md;

    &:active {
      background-color: $primary-light;
    }
  }

  .text-btn {
    width: 100%;
    height: 72rpx;
    background-color: transparent;
    color: $text-secondary;
    border: none;
    font-size: $font-size-sm;

    &:active {
      color: $primary-color;
    }
  }
}

.help-section {
  .help-title {
    display: block;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;
    color: $text-primary;
    margin-bottom: $spacing-md;
    text-align: center;
  }

  .help-contacts {
    .contact-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: $spacing-md;
      margin-bottom: $spacing-sm;
      background-color: $surface-color;
      border-radius: $border-radius-medium;

      .contact-text {
        margin-left: $spacing-sm;
        font-size: $font-size-sm;
        color: $text-secondary;
      }

      &:active {
        background-color: $background-color;
      }
    }
  }
}
</style>
