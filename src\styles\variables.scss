/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */

// ==================== 主色彩 ====================
$primary-color: #1976d2;        // 主蓝色
$primary-light: #42a5f5;        // 浅蓝色
$primary-dark: #1565c0;         // 深蓝色

$secondary-color: #4caf50;       // 主绿色
$secondary-light: #81c784;       // 浅绿色
$secondary-dark: #388e3c;        // 深绿色

$accent-color: #ff9800;          // 强调色(橙色)
$accent-light: #ffb74d;          // 浅橙色

// ==================== 功能色彩 ====================
$success-color: #4caf50;         // 成功色
$success-light: #81c784;         // 成功色-浅色
$success-dark: #388e3c;          // 成功色-深色
$warning-color: #ff9800;         // 警告色
$warning-light: #ffcc02;         // 警告色-浅色
$warning-dark: #f57c00;          // 警告色-深色
$error-color: #f44336;           // 错误色
$error-light: #ef5350;           // 错误色-浅色
$error-dark: #d32f2f;            // 错误色-深色
$info-color: #2196f3;            // 信息色
$info-light: #64b5f6;            // 信息色-浅色
$info-dark: #1976d2;             // 信息色-深色

// ==================== 中性色彩 ====================
$text-primary: #212121;          // 主要文字
$text-secondary: #757575;        // 次要文字
$text-disabled: #bdbdbd;         // 禁用文字
$text-hint: #9e9e9e;             // 提示文字

$divider-color: #e0e0e0;         // 分割线
$border-color: #e0e0e0;          // 边框色
$background-color: #fafafa;      // 背景色
$surface-color: #ffffff;         // 表面色

// ==================== 状态色彩 ====================
$status-pending: #ff9800;        // 待审核
$status-approved: #4caf50;       // 已通过
$status-rejected: #f44336;       // 已拒绝
$status-expired: #9e9e9e;        // 已过期

// ==================== 尺寸变量 ====================
$border-radius-small: 4rpx;      // 小圆角
$border-radius-medium: 8rpx;     // 中圆角
$border-radius-large: 12rpx;     // 大圆角
$border-radius-xl: 16rpx;        // 超大圆角

$spacing-xs: 8rpx;               // 超小间距
$spacing-sm: 16rpx;              // 小间距
$spacing-md: 24rpx;              // 中间距
$spacing-lg: 32rpx;              // 大间距
$spacing-xl: 48rpx;              // 超大间距
$spacing-xxl: 64rpx;             // 超超大间距

// ==================== 字体变量 ====================
$font-size-xs: 20rpx;            // 超小字体
$font-size-sm: 24rpx;            // 小字体
$font-size-md: 28rpx;            // 中字体
$font-size-lg: 32rpx;            // 大字体
$font-size-xl: 36rpx;            // 超大字体
$font-size-xxl: 40rpx;           // 标题字体

$font-weight-normal: 400;        // 正常字重
$font-weight-medium: 500;        // 中等字重
$font-weight-bold: 600;          // 粗体字重

// 行高倍数系统
$line-height-tight: 1.2;         // 紧密行高
$line-height-normal: 1.4;        // 正常行高
$line-height-loose: 1.6;         // 宽松行高

// 具体行高值（用于uview-plus组件）
$line-height-xs: 28rpx;          // 超小字体行高 (20*1.4)
$line-height-sm: 34rpx;          // 小字体行高 (24*1.4)
$line-height-md: 39rpx;          // 中字体行高 (28*1.4)
$line-height-lg: 45rpx;          // 大字体行高 (32*1.4)
$line-height-xl: 50rpx;          // 超大字体行高 (36*1.4)
$line-height-xxl: 56rpx;         // 标题字体行高 (40*1.4)

// ==================== 阴影变量 ====================
$shadow-light: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-medium: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
$shadow-heavy: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);

// ==================== 动画变量 ====================
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

$headline-font-size: 40rpx;
$headline-line-height: 56rpx;
$subline-font-size: 30rpx;
$subline-line-height: 42rpx;

$login-bg-main: #1e88e5;         // 主背景色 - 降低明度
$login-bg-light: #4a90e2;        // 渐变终点 - 调整为更柔和的蓝色
$login-bg-dark: #1565c0;         // 深色模式主色
$login-bg-night: #1a237e;        // 深色模式深色
$login-bg-glow1: rgba(255,255,255,0.08);  // 光斑1 - 降低透明度
$login-bg-glow2: rgba(255,255,255,0.05);  // 光斑2 - 降低透明度
