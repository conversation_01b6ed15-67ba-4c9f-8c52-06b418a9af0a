<template>
  <view class="offline-exam-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <u-loading-icon mode="spinner" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 考试详情 -->
    <view v-else-if="examInfo" class="exam-content">
      <!-- 考试基本信息 -->
      <view class="exam-header">
        <text class="exam-title">{{ examInfo.name }}</text>
        <view class="exam-status" :class="examInfo.status">
          {{ getStatusText(examInfo.status) }}
        </view>
      </view>

      <!-- 考试详细信息 -->
      <view class="exam-details">
        <view class="detail-section">
          <text class="section-title">考试信息</text>
          
          <view class="detail-items">
            <view class="detail-item">
              <view class="item-icon">📅</view>
              <view class="item-content">
                <text class="item-label">考试时间</text>
                <text class="item-value">{{ formatDateTime(examInfo.examTime) }}</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">📍</view>
              <view class="item-content">
                <text class="item-label">考试地点</text>
                <text class="item-value">{{ examInfo.location }}</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">⏱️</view>
              <view class="item-content">
                <text class="item-label">考试时长</text>
                <text class="item-value">{{ examInfo.duration }}分钟</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">📝</view>
              <view class="item-content">
                <text class="item-label">题目数量</text>
                <text class="item-value">{{ examInfo.totalQuestions }}题</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">🎯</view>
              <view class="item-content">
                <text class="item-label">及格分数</text>
                <text class="item-value">{{ examInfo.passScore }}分</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 报名信息 -->
        <view class="detail-section">
          <text class="section-title">报名信息</text>
          
          <view class="detail-items">
            <view class="detail-item">
              <view class="item-icon">📋</view>
              <view class="item-content">
                <text class="item-label">报名截止</text>
                <text class="item-value">{{ formatDateTime(examInfo.registrationDeadline) }}</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">👥</view>
              <view class="item-content">
                <text class="item-label">报名人数</text>
                <text class="item-value">{{ examInfo.registeredCount }}/{{ examInfo.maxCapacity }}</text>
              </view>
            </view>
            
            <view class="detail-item">
              <view class="item-icon">💰</view>
              <view class="item-content">
                <text class="item-label">考试费用</text>
                <text class="item-value">{{ examInfo.fee > 0 ? `¥${examInfo.fee}` : '免费' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 考试说明 -->
        <view class="detail-section">
          <text class="section-title">考试说明</text>
          
          <view class="exam-description">
            <text class="description-text">{{ examInfo.description }}</text>
          </view>
          
          <view class="exam-requirements">
            <text class="requirements-title">考试要求：</text>
            <view class="requirements-list">
              <view v-for="requirement in examInfo.requirements" :key="requirement" class="requirement-item">
                <text class="requirement-icon">•</text>
                <text class="requirement-text">{{ requirement }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 携带物品 -->
        <view class="detail-section">
          <text class="section-title">携带物品</text>
          
          <view class="items-grid">
            <view v-for="item in examInfo.requiredItems" :key="item" class="item-card">
              <view class="item-icon">{{ getItemIcon(item) }}</view>
              <text class="item-name">{{ item }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 报名状态和操作 -->
      <view class="registration-section">
        <!-- 已报名状态 -->
        <view v-if="registrationInfo" class="registered-info">
          <view class="registered-header">
            <u-icon name="checkmark-circle-fill" size="24" color="#4caf50" />
            <text class="registered-title">报名成功</text>
          </view>
          
          <view class="registered-details">
            <view class="registered-item">
              <text class="registered-label">报名时间：</text>
              <text class="registered-value">{{ formatDateTime(registrationInfo.registrationTime) }}</text>
            </view>
            
            <view class="registered-item">
              <text class="registered-label">准考证号：</text>
              <text class="registered-value">{{ registrationInfo.admissionNumber }}</text>
            </view>
            
            <view class="registered-item">
              <text class="registered-label">座位号：</text>
              <text class="registered-value">{{ registrationInfo.seatNumber || '待分配' }}</text>
            </view>
          </view>
          
          <view class="registered-actions">
            <button class="action-btn secondary" @tap="downloadAdmissionTicket">
              下载准考证
            </button>
            
            <button 
              v-if="canCancelRegistration"
              class="action-btn danger"
              @tap="showCancelModal"
            >
              取消报名
            </button>
          </view>
        </view>

        <!-- 未报名状态 -->
        <view v-else class="registration-actions">
          <view v-if="!canRegister" class="registration-disabled">
            <text class="disabled-text">{{ getDisabledReason() }}</text>
          </view>
          
          <button 
            v-else
            class="register-btn"
            @tap="showRegisterModal"
          >
            立即报名
          </button>
        </view>
      </view>
    </view>

    <!-- 报名确认弹窗 -->
    <u-modal
      v-model="showRegisterConfirm"
      title="确认报名"
      :content="registerModalContent"
      show-cancel-button
      @confirm="confirmRegistration"
      @cancel="showRegisterConfirm = false"
    />

    <!-- 取消报名确认弹窗 -->
    <u-modal
      v-model="showCancelConfirm"
      title="取消报名"
      content="确定要取消报名吗？取消后需要重新报名。"
      show-cancel-button
      confirm-color="#f44336"
      @confirm="confirmCancelRegistration"
      @cancel="showCancelConfirm = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { getExamDetail, registerExam, cancelExamRegistration, getRegistrationInfo } from '../../../../src/api/modules/exam';

// 页面参数
const props = defineProps<{
  id: string;
}>();

// 响应式数据
const examInfo = ref<any>(null);
const registrationInfo = ref<any>(null);
const loading = ref(false);
const showRegisterConfirm = ref(false);
const showCancelConfirm = ref(false);

// 计算属性
const canRegister = computed(() => {
  if (!examInfo.value) return false;
  
  const now = new Date();
  const deadline = new Date(examInfo.value.registrationDeadline);
  const isFull = examInfo.value.registeredCount >= examInfo.value.maxCapacity;
  
  return now < deadline && !isFull && examInfo.value.status === 'open';
});

const canCancelRegistration = computed(() => {
  if (!registrationInfo.value || !examInfo.value) return false;
  
  const now = new Date();
  const examTime = new Date(examInfo.value.examTime);
  const hoursBeforeExam = (examTime.getTime() - now.getTime()) / (1000 * 60 * 60);
  
  return hoursBeforeExam > 24; // 考试前24小时内不能取消
});

const registerModalContent = computed(() => {
  if (!examInfo.value) return '';
  
  let content = `确定要报名参加"${examInfo.value.name}"吗？\n\n`;
  content += `考试时间：${formatDateTime(examInfo.value.examTime)}\n`;
  content += `考试地点：${examInfo.value.location}\n`;
  
  if (examInfo.value.fee > 0) {
    content += `考试费用：¥${examInfo.value.fee}`;
  }
  
  return content;
});

onMounted(() => {
  loadExamInfo();
});

/**
 * 加载考试信息
 */
async function loadExamInfo() {
  try {
    loading.value = true;

    // 并行加载考试信息和报名信息
    const [examData, registrationData] = await Promise.all([
      getExamDetail(props.id),
      getRegistrationInfo(props.id).catch(() => null) // 如果未报名会返回错误，这里忽略
    ]);

    examInfo.value = examData;
    registrationInfo.value = registrationData;

  } catch (error) {
    console.error('加载考试信息失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    open: '报名中',
    closed: '报名结束',
    full: '报名已满',
    completed: '已结束',
    cancelled: '已取消'
  };
  return statusMap[status] || status;
}

/**
 * 获取禁用原因
 */
function getDisabledReason() {
  if (!examInfo.value) return '';

  const now = new Date();
  const deadline = new Date(examInfo.value.registrationDeadline);
  const isFull = examInfo.value.registeredCount >= examInfo.value.maxCapacity;

  if (now >= deadline) {
    return '报名已截止';
  } else if (isFull) {
    return '报名人数已满';
  } else if (examInfo.value.status === 'closed') {
    return '报名已关闭';
  } else if (examInfo.value.status === 'cancelled') {
    return '考试已取消';
  }

  return '暂不可报名';
}

/**
 * 获取物品图标
 */
function getItemIcon(item: string) {
  const iconMap: Record<string, string> = {
    '身份证': '🆔',
    '准考证': '📄',
    '2B铅笔': '✏️',
    '橡皮': '🧽',
    '黑色签字笔': '🖊️',
    '计算器': '🧮',
    '尺子': '📏'
  };
  return iconMap[item] || '📦';
}

/**
 * 显示报名确认弹窗
 */
function showRegisterModal() {
  showRegisterConfirm.value = true;
}

/**
 * 显示取消报名确认弹窗
 */
function showCancelModal() {
  showCancelConfirm.value = true;
}

/**
 * 确认报名
 */
async function confirmRegistration() {
  try {
    showRegisterConfirm.value = false;
    uni.showLoading({ title: '报名中...' });

    const result = await registerExam(props.id);

    uni.hideLoading();

    // 更新报名信息
    registrationInfo.value = result;

    // 更新考试信息中的报名人数
    if (examInfo.value) {
      examInfo.value.registeredCount++;
    }

    uni.showToast({
      title: '报名成功',
      icon: 'success'
    });

  } catch (error) {
    uni.hideLoading();
    console.error('报名失败:', error);
    uni.showToast({
      title: '报名失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 确认取消报名
 */
async function confirmCancelRegistration() {
  try {
    showCancelConfirm.value = false;
    uni.showLoading({ title: '取消中...' });

    await cancelExamRegistration(props.id);

    uni.hideLoading();

    // 清除报名信息
    registrationInfo.value = null;

    // 更新考试信息中的报名人数
    if (examInfo.value) {
      examInfo.value.registeredCount--;
    }

    uni.showToast({
      title: '取消成功',
      icon: 'success'
    });

  } catch (error) {
    uni.hideLoading();
    console.error('取消报名失败:', error);
    uni.showToast({
      title: '取消失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 下载准考证
 */
function downloadAdmissionTicket() {
  if (!registrationInfo.value) return;

  // 这里实现准考证下载逻辑
  uni.showModal({
    title: '下载准考证',
    content: '准考证将保存到相册，请在考试当天携带打印版本',
    showCancel: false,
    success: () => {
      // 实际项目中这里会调用下载API
      uni.showToast({
        title: '准考证已保存',
        icon: 'success'
      });
    }
  });
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr: string) {
  const date = new Date(dateTimeStr);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.offline-exam-container {
  min-height: 100vh;
  background-color: $background-color;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-text {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.exam-content {
  .exam-header {
    background: linear-gradient(135deg, $secondary-color, $secondary-light);
    padding: $spacing-xl $spacing-lg;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .exam-title {
      flex: 1;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: white;
      margin-right: $spacing-md;
    }

    .exam-status {
      padding: $spacing-xs $spacing-sm;
      border-radius: $border-radius-small;
      font-size: $font-size-xs;
      color: white;

      &.open {
        background-color: $success-color;
      }

      &.closed, &.full {
        background-color: $error-color;
      }

      &.completed {
        background-color: $text-disabled;
      }
    }
  }

  .exam-details {
    padding: $spacing-lg;

    .detail-section {
      background-color: $surface-color;
      border-radius: $border-radius-large;
      padding: $spacing-xl;
      margin-bottom: $spacing-lg;
      box-shadow: $shadow-light;

      .section-title {
        display: block;
        font-size: $font-size-lg;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-bottom: $spacing-lg;
        padding-bottom: $spacing-sm;
        border-bottom: 2rpx solid $divider-color;
      }

      .detail-items {
        .detail-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: $spacing-lg;

          &:last-child {
            margin-bottom: 0;
          }

          .item-icon {
            font-size: $font-size-lg;
            margin-right: $spacing-md;
            margin-top: 4rpx;
          }

          .item-content {
            flex: 1;

            .item-label {
              display: block;
              font-size: $font-size-sm;
              color: $text-secondary;
              margin-bottom: $spacing-xs;
            }

            .item-value {
              font-size: $font-size-md;
              color: $text-primary;
              font-weight: $font-weight-medium;
            }
          }
        }
      }

      .exam-description {
        margin-bottom: $spacing-lg;

        .description-text {
          font-size: $font-size-md;
          line-height: 1.6;
          color: $text-secondary;
        }
      }

      .exam-requirements {
        .requirements-title {
          display: block;
          font-size: $font-size-md;
          font-weight: $font-weight-medium;
          color: $text-primary;
          margin-bottom: $spacing-md;
        }

        .requirements-list {
          .requirement-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: $spacing-sm;

            .requirement-icon {
              color: $secondary-color;
              margin-right: $spacing-sm;
              margin-top: 2rpx;
            }

            .requirement-text {
              flex: 1;
              font-size: $font-size-sm;
              color: $text-secondary;
              line-height: 1.5;
            }
          }
        }
      }

      .items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
        gap: $spacing-md;

        .item-card {
          background-color: $background-color;
          border-radius: $border-radius-medium;
          padding: $spacing-lg;
          text-align: center;

          .item-icon {
            display: block;
            font-size: 60rpx;
            margin-bottom: $spacing-sm;
          }

          .item-name {
            font-size: $font-size-sm;
            color: $text-secondary;
          }
        }
      }
    }
  }

  .registration-section {
    padding: 0 $spacing-lg $spacing-lg;

    .registered-info {
      background-color: $success-light;
      border-radius: $border-radius-large;
      padding: $spacing-xl;

      .registered-header {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        margin-bottom: $spacing-lg;

        .registered-title {
          font-size: $font-size-lg;
          font-weight: $font-weight-medium;
          color: $success-color;
        }
      }

      .registered-details {
        margin-bottom: $spacing-lg;

        .registered-item {
          display: flex;
          align-items: center;
          margin-bottom: $spacing-md;

          .registered-label {
            font-size: $font-size-sm;
            color: $text-secondary;
            margin-right: $spacing-sm;
          }

          .registered-value {
            font-size: $font-size-md;
            color: $text-primary;
            font-weight: $font-weight-medium;
          }
        }
      }

      .registered-actions {
        display: flex;
        gap: $spacing-md;

        .action-btn {
          flex: 1;
          height: 72rpx;
          border: none;
          border-radius: $border-radius-medium;
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;

          &.secondary {
            background-color: white;
            color: $success-color;
            border: 2rpx solid $success-color;
          }

          &.danger {
            background-color: $error-color;
            color: white;
          }
        }
      }
    }

    .registration-actions {
      .registration-disabled {
        background-color: $surface-color;
        border-radius: $border-radius-large;
        padding: $spacing-xl;
        text-align: center;

        .disabled-text {
          font-size: $font-size-md;
          color: $text-disabled;
        }
      }

      .register-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, $secondary-color, $secondary-dark);
        color: white;
        border: none;
        border-radius: $border-radius-large;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
