/**
 * 证书管理相关API接口
 */
import http from '../../utils/request';
import type { Certificate } from '../../types/api';

/**
 * 获取证书列表
 */
export function getCertificateList(params?: { filter?: string }) {
  return http.get<Certificate[]>('/certificate/list', params);
}

/**
 * 获取证书详情
 */
export function getCertificateDetail(certificateId: string) {
  return http.get<Certificate>(`/certificate/detail/${certificateId}`);
}

/**
 * 下载证书文件
 */
export function downloadCertificateFile(certificateId: string) {
  return http.get<{ downloadUrl: string }>(`/certificate/download/${certificateId}`);
}

/**
 * 申请证书续期
 */
export function renewCertificate(certificateId: string) {
  return http.post<boolean>(`/certificate/renew/${certificateId}`);
}

/**
 * 验证证书有效性
 */
export function verifyCertificate(certificateNumber: string) {
  return http.get<{ valid: boolean; certificate?: Certificate }>(`/certificate/verify/${certificateNumber}`);
}
