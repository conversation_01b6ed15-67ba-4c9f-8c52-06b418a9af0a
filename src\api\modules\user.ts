/**
 * 用户相关API接口
 * 更新时间：2025-06-20T11:00:39
 */
import http from '../../utils/request';
import type { UserInfo, LoginParams, RegisterParams, WxLoginData, Institution, Position } from '../../types/api';

/**
 * 微信登录 - 使用统一包裹结构处理
 */
export function wxLogin(params: LoginParams) {
  return http.post<WxLoginData>('/auth/wechat-login', params);
}

/**
 * 获取机构列表
 */
export function getInstitutions() {
  return http.get<Institution[]>('/institutions');
}

/**
 * 获取职位列表
 */
export function getPositions() {
  return http.get<Position[]>('/positions');
}

/**
 * 提交用户注册信息
 */
export function submitUserInfo(params: RegisterParams) {
  return http.post<boolean>('/profile', params);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>('/profile/me');
}

/**
 * 上传用户头像
 */
export function uploadAvatar(file: File) {
  return http.upload<string>('/user/upload-avatar', { file });
}

/**
 * 获取用户统计数据
 */
export function getUserStats() {
  return http.get<any>('/profile/stats');
}
