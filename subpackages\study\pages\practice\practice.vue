<template>
  <view class="practice-container">
    <!-- 练习头部 -->
    <view class="practice-header">
      <view class="header-info">
        <text class="category-name">{{ categoryName }}</text>
        <text class="progress-text">{{ currentIndex + 1 }}/{{ questionList.length }}</text>
      </view>
      
      <view class="timer-section">
        <u-icon name="clock" size="16" color="#ff9800" />
        <text class="timer-text">{{ formatTime(remainingTime) }}</text>
      </view>
    </view>

    <!-- 进度条 -->
    <view class="progress-bar">
      <view 
        class="progress-fill" 
        :style="{ width: progressPercent + '%' }"
      ></view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <u-loading-icon mode="spinner" />
      <text class="loading-text">加载题目中...</text>
    </view>

    <!-- 题目内容 -->
    <view v-else-if="currentQuestion" class="question-content">
      <!-- 题目信息 -->
      <view class="question-header">
        <view class="question-type">{{ getQuestionTypeText(currentQuestion.type) }}</view>
        <view class="question-difficulty" :class="currentQuestion.difficulty">
          {{ getDifficultyText(currentQuestion.difficulty) }}
        </view>
      </view>

      <!-- 题目题干 -->
      <view class="question-stem">
        <text class="question-text">{{ currentQuestion.stem }}</text>
        
        <!-- 题目图片 -->
        <image 
          v-if="currentQuestion.image" 
          class="question-image"
          :src="currentQuestion.image"
          mode="widthFix"
          @tap="previewImage(currentQuestion.image)"
        />
      </view>

      <!-- 选项列表 -->
      <view class="options-list">
        <view 
          v-for="(option, index) in currentQuestion.options" 
          :key="index"
          class="option-item"
          :class="{ 
            selected: selectedAnswers.includes(option.key),
            correct: showAnswer && option.isCorrect,
            wrong: showAnswer && selectedAnswers.includes(option.key) && !option.isCorrect
          }"
          @tap="selectOption(option.key)"
        >
          <view class="option-key">{{ option.key }}</view>
          <text class="option-text">{{ option.text }}</text>
          
          <!-- 答案标识 -->
          <view v-if="showAnswer" class="answer-indicator">
            <u-icon 
              v-if="option.isCorrect" 
              name="checkmark-circle-fill" 
              size="20" 
              color="#4caf50" 
            />
            <u-icon 
              v-else-if="selectedAnswers.includes(option.key)" 
              name="close-circle-fill" 
              size="20" 
              color="#f44336" 
            />
          </view>
        </view>
      </view>

      <!-- 答案解析 -->
      <view v-if="showAnswer && currentQuestion.explanation" class="explanation-section">
        <text class="explanation-title">答案解析</text>
        <text class="explanation-content">{{ currentQuestion.explanation }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        v-if="!showAnswer"
        class="submit-btn"
        :disabled="selectedAnswers.length === 0"
        @tap="submitAnswer"
      >
        确认答案
      </button>
      
      <view v-else class="answer-actions">
        <button 
          v-if="currentIndex > 0"
          class="prev-btn"
          @tap="prevQuestion"
        >
          上一题
        </button>
        
        <button 
          v-if="currentIndex < questionList.length - 1"
          class="next-btn"
          @tap="nextQuestion"
        >
          下一题
        </button>
        
        <button 
          v-else
          class="finish-btn"
          @tap="finishPractice"
        >
          完成练习
        </button>
      </view>
    </view>

    <!-- 退出确认弹窗 -->
    <u-modal
      v-model="showExitModal"
      title="确认退出"
      content="练习尚未完成，确定要退出吗？"
      show-cancel-button
      @confirm="confirmExit"
      @cancel="showExitModal = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { getPracticeQuestions, submitPracticeResult } from '../../../../src/api/modules/study';

// 页面参数
const props = defineProps<{
  categoryId: string;
  categoryName: string;
}>();

// 响应式数据
const questionList = ref<any[]>([]);
const currentIndex = ref(0);
const selectedAnswers = ref<string[]>([]);
const showAnswer = ref(false);
const loading = ref(false);
const remainingTime = ref(30 * 60); // 30分钟
const showExitModal = ref(false);
const answerResults = ref<any[]>([]);

// 计算属性
const currentQuestion = computed(() => questionList.value[currentIndex.value]);
const progressPercent = computed(() => 
  questionList.value.length > 0 ? ((currentIndex.value + 1) / questionList.value.length) * 100 : 0
);

// 定时器
let timer: NodeJS.Timeout | null = null;

onMounted(() => {
  loadQuestions();
  startTimer();
  
  // 监听页面返回
  uni.onBackPress(() => {
    showExitModal.value = true;
    return true;
  });
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});

/**
 * 加载题目
 */
async function loadQuestions() {
  try {
    loading.value = true;
    questionList.value = await getPracticeQuestions({
      categoryId: props.categoryId,
      count: 20
    });
  } catch (error) {
    console.error('加载题目失败:', error);
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  } finally {
    loading.value = false;
  }
}

/**
 * 开始计时
 */
function startTimer() {
  timer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      // 时间到，自动提交
      finishPractice();
    }
  }, 1000);
}

/**
 * 选择选项
 */
function selectOption(optionKey: string) {
  if (showAnswer.value) return;
  
  const question = currentQuestion.value;
  
  if (question.type === 'single') {
    // 单选题
    selectedAnswers.value = [optionKey];
  } else if (question.type === 'multiple') {
    // 多选题
    const index = selectedAnswers.value.indexOf(optionKey);
    if (index > -1) {
      selectedAnswers.value.splice(index, 1);
    } else {
      selectedAnswers.value.push(optionKey);
    }
  }
}

/**
 * 提交答案
 */
async function submitAnswer() {
  if (selectedAnswers.value.length === 0) return;
  
  try {
    const result = await submitPracticeResult({
      questionId: currentQuestion.value.id,
      answers: selectedAnswers.value
    });
    
    // 保存答题结果
    answerResults.value.push({
      questionId: currentQuestion.value.id,
      answers: selectedAnswers.value,
      isCorrect: result.isCorrect,
      score: result.score
    });
    
    showAnswer.value = true;
    
  } catch (error) {
    console.error('提交答案失败:', error);
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none'
    });
  }
}

/**
 * 上一题
 */
function prevQuestion() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    resetQuestionState();
  }
}

/**
 * 下一题
 */
function nextQuestion() {
  if (currentIndex.value < questionList.value.length - 1) {
    currentIndex.value++;
    resetQuestionState();
  }
}

/**
 * 重置题目状态
 */
function resetQuestionState() {
  selectedAnswers.value = [];
  showAnswer.value = false;
  
  // 如果已经答过这题，显示之前的答案
  const prevResult = answerResults.value.find(r => r.questionId === currentQuestion.value.id);
  if (prevResult) {
    selectedAnswers.value = prevResult.answers;
    showAnswer.value = true;
  }
}

/**
 * 完成练习
 */
function finishPractice() {
  if (timer) {
    clearInterval(timer);
  }
  
  // 跳转到结果页面
  const totalScore = answerResults.value.reduce((sum, result) => sum + result.score, 0);
  const correctCount = answerResults.value.filter(result => result.isCorrect).length;
  
  uni.redirectTo({
    url: `/subpackages/study/pages/practice-result/practice-result?categoryName=${props.categoryName}&totalScore=${totalScore}&correctCount=${correctCount}&totalCount=${questionList.value.length}`
  });
}

/**
 * 确认退出
 */
function confirmExit() {
  if (timer) {
    clearInterval(timer);
  }
  uni.navigateBack();
}

/**
 * 预览图片
 */
function previewImage(imageUrl: string) {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  });
}

/**
 * 获取题目类型文本
 */
function getQuestionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题'
  };
  return typeMap[type] || type;
}

/**
 * 获取难度文本
 */
function getDifficultyText(difficulty: string) {
  const difficultyMap: Record<string, string> = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  };
  return difficultyMap[difficulty] || difficulty;
}

/**
 * 格式化时间
 */
function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.practice-container {
  min-height: 100vh;
  background-color: $background-color;
  display: flex;
  flex-direction: column;
}

.practice-header {
  background-color: $surface-color;
  padding: $spacing-md $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid $divider-color;

  .header-info {
    .category-name {
      display: block;
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-primary;
      margin-bottom: $spacing-xs;
    }

    .progress-text {
      font-size: $font-size-sm;
      color: $text-secondary;
    }
  }

  .timer-section {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .timer-text {
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      color: $warning-color;
    }
  }
}

.progress-bar {
  height: 6rpx;
  background-color: $divider-color;

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, $success-color, $success-light);
    transition: width 0.3s ease;
  }
}

.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .loading-text {
    margin-top: $spacing-md;
    font-size: $font-size-md;
    color: $text-secondary;
  }
}

.question-content {
  flex: 1;
  padding: $spacing-lg;

  .question-header {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    margin-bottom: $spacing-lg;

    .question-type {
      background-color: $primary-color;
      color: white;
      font-size: $font-size-xs;
      padding: 4rpx 12rpx;
      border-radius: $border-radius-small;
    }

    .question-difficulty {
      font-size: $font-size-xs;
      padding: 4rpx 12rpx;
      border-radius: $border-radius-small;

      &.easy {
        background-color: $success-light;
        color: $success-color;
      }

      &.medium {
        background-color: $warning-light;
        color: $warning-color;
      }

      &.hard {
        background-color: $error-light;
        color: $error-color;
      }
    }
  }

  .question-stem {
    background-color: $surface-color;
    border-radius: $border-radius-large;
    padding: $spacing-xl;
    margin-bottom: $spacing-lg;

    .question-text {
      font-size: $font-size-lg;
      line-height: 1.6;
      color: $text-primary;
      display: block;
      margin-bottom: $spacing-md;
    }

    .question-image {
      width: 100%;
      border-radius: $border-radius-medium;
      margin-top: $spacing-md;
    }
  }

  .options-list {
    .option-item {
      display: flex;
      align-items: flex-start;
      background-color: $surface-color;
      border: 2rpx solid transparent;
      border-radius: $border-radius-medium;
      padding: $spacing-lg;
      margin-bottom: $spacing-md;
      transition: all 0.2s ease;

      &.selected {
        border-color: $primary-color;
        background-color: $primary-light;
      }

      &.correct {
        border-color: $success-color;
        background-color: $success-light;
      }

      &.wrong {
        border-color: $error-color;
        background-color: $error-light;
      }

      .option-key {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: $background-color;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: $font-size-md;
        font-weight: $font-weight-medium;
        color: $text-primary;
        margin-right: $spacing-md;
        flex-shrink: 0;
      }

      .option-text {
        flex: 1;
        font-size: $font-size-md;
        line-height: 1.5;
        color: $text-primary;
        margin-right: $spacing-md;
      }

      .answer-indicator {
        flex-shrink: 0;
      }
    }
  }

  .explanation-section {
    background-color: $info-light;
    border-radius: $border-radius-medium;
    padding: $spacing-lg;
    margin-top: $spacing-lg;

    .explanation-title {
      display: block;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
      color: $info-color;
      margin-bottom: $spacing-sm;
    }

    .explanation-content {
      font-size: $font-size-sm;
      line-height: 1.6;
      color: $text-secondary;
    }
  }
}

.action-buttons {
  padding: $spacing-lg;
  border-top: 1rpx solid $divider-color;
  background-color: $surface-color;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, $primary-color, $primary-dark);
    color: white;
    border: none;
    border-radius: $border-radius-large;
    font-size: $font-size-md;
    font-weight: $font-weight-medium;

    &:disabled {
      background: $text-disabled;
      opacity: 0.6;
    }
  }

  .answer-actions {
    display: flex;
    gap: $spacing-md;

    .prev-btn, .next-btn, .finish-btn {
      flex: 1;
      height: 88rpx;
      border: none;
      border-radius: $border-radius-large;
      font-size: $font-size-md;
      font-weight: $font-weight-medium;
    }

    .prev-btn {
      background-color: $text-disabled;
      color: white;
    }

    .next-btn {
      background: linear-gradient(135deg, $primary-color, $primary-dark);
      color: white;
    }

    .finish-btn {
      background: linear-gradient(135deg, $success-color, $success-dark);
      color: white;
    }
  }
}
</style>
