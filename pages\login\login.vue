<!--
  疾控考试系统 - 登录页面
  复选框修复版本 - 2025-06-17T23:48:55

  设计理念：
  - 医疗健康主题配色（浅蓝+白色，绿蓝渐变）
  - 垂直居中布局，按钮位于60-70%高度
  - 清晰的视觉反馈和用户体验
  - 严格遵循uview-plus组件规范

  修复内容：
  - 修复复选框勾选标记不显示问题
  - 改用u-checkbox-group标准组件模式
  - 优化复选框样式和视觉反馈
-->
<template>
  <view class="login-page">
    <!-- 背景装饰层 -->
    <view class="background-decoration">
      <view class="decoration-circle decoration-circle--1"></view>
      <view class="decoration-circle decoration-circle--2"></view>
      <view class="decoration-circle decoration-circle--3"></view>
    </view>

    <!-- 主要内容容器 -->
    <view class="main-container">
      <!-- 顶部品牌区域 -->
      <view class="brand-section">
        <view class="logo-wrapper">
          <view class="logo-background">
            <u-icon name="medical-bag" size="48" color="#ffffff" />
          </view>
        </view>
        <view class="brand-text">
          <text class="main-title">疾控考试系统</text>
          <text class="sub-title">医护任职资格考试平台</text>
        </view>
      </view>

      <!-- 登录操作区域 - 位于60%高度 -->
      <view class="login-section">
        <!-- 微信登录按钮 -->
        <view class="login-button-wrapper">
          <u-button
            type="primary"
            text="微信授权登录"
            icon="weixin-fill"
            :disabled="!agreedToTerms || isLoading"
            :loading="isLoading"
            loadingText="登录中..."
            :customStyle="loginButtonStyle"
            shape="circle"
            size="large"
            :throttleTime="1000"
            @click="handleWxLogin"
          />
        </view>

        <!-- 协议确认区域 -->
        <view class="agreement-section">
          <view class="checkbox-container">
            <u-checkbox
              v-model:checked="agreedToTerms"
              activeColor="#4CAF50"       
              inactiveColor="#e0e0e0"
              size="20"
              iconSize="14"
              shape="circle"
              :disabled="isLoading"
              usedAlone
              label="我已阅读并同意"
            />
          </view>
          <view class="agreement-links">
            <text class="agreement-link" @click="showUserAgreement">《用户服务协议》</text>
            <text class="agreement-text">和</text>
            <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户协议模态框 -->
    <u-modal
      v-model:show="showUserAgreementModal"
      title="用户服务协议"
      :showCancelButton="false"
      confirmText="我知道了"
      confirmColor="#4CAF50"
      @confirm="showUserAgreementModal = false"
    >
      <view class="modal-content">
        <text class="modal-text">{{ userAgreementContent }}</text>
      </view>
    </u-modal>

    <!-- 隐私政策模态框 -->
    <u-modal
      v-model:show="showPrivacyPolicyModal"
      title="隐私政策"
      :showCancelButton="false"
      confirmText="我知道了"
      confirmColor="#4CAF50"
      @confirm="showPrivacyPolicyModal = false"
    >
      <view class="modal-content">
        <text class="modal-text">{{ privacyPolicyContent }}</text>
      </view>
    </u-modal>

    <!-- Toast 消息提示 -->
    <u-toast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/src/stores/modules/user'
import { wxLogin, getUserInfo } from '@/src/api/modules/user'
import type { LoginParams, UserInfo, WxLoginData } from '@/src/types/api'

// ==================== Interfaces ====================
interface ToastInstance {
  show: (options: {
    title: string
    type?: 'success' | 'error' | 'warning' | 'info'
    duration?: number
  }) => void
}

// ==================== Store ====================
const userStore = useUserStore()
const { profile } = storeToRefs(userStore)
const { setProfile } = userStore

// ==================== 响应式数据 ====================
/** 是否同意用户协议 */
const agreedToTerms = ref<boolean>(false)
/** 登录加载状态 */
const isLoading = ref<boolean>(false)
/** 显示用户协议模态框 */
const showUserAgreementModal = ref<boolean>(false)
/** 显示隐私政策模态框 */
const showPrivacyPolicyModal = ref<boolean>(false)

// ==================== Toast 引用 ====================
const toastRef = ref<ToastInstance | null>(null)

// ==================== 设计系统 ====================
/** 登录按钮样式 - 医疗健康主题 */
const loginButtonStyle = computed(() => ({
  width: '100%',
  height: '96rpx',
  background: agreedToTerms.value
    ? 'linear-gradient(135deg, #4CAF50, #2196F3)'
    : '#e0e0e0',
  color: '#ffffff',
  border: 'none',
  borderRadius: '48rpx',
  boxShadow: agreedToTerms.value
    ? '0 8rpx 24rpx rgba(76, 175, 80, 0.3), 0 4rpx 12rpx rgba(33, 150, 243, 0.2)'
    : '0 2rpx 8rpx rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  opacity: agreedToTerms.value ? 1 : 0.6,
  transform: agreedToTerms.value ? 'translateY(0)' : 'translateY(2rpx)',
}))

/** 用户协议内容 */
const userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`)

/** 隐私政策内容 */
const privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`)

// ==================== 事件处理 ====================


/**
 * 显示用户服务协议
 */
function showUserAgreement(): void {
  showUserAgreementModal.value = true
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy(): void {
  showPrivacyPolicyModal.value = true
}

/**
 * 显示Toast消息
 * @param title 消息标题
 * @param type 消息类型
 */
function showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
  if (toastRef.value) {
    toastRef.value.show({
      title,
      type,
      duration: type === 'success' ? 1500 : 2000,
    })
  }
}

/**
 * 微信授权登录
 */
async function handleWxLogin(): Promise<void> {
  // 检查协议同意状态
  if (!agreedToTerms.value) {
    showToast('请先同意用户协议', 'warning')
    return
  }

  isLoading.value = true

  try {
    // 调用微信登录获取code
    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      })
    })

    // 构造登录参数
    const loginParams: LoginParams = {
      code: loginResult.code,
    }

    // 调用后端登录接口 - 响应拦截器已自动处理包裹结构
    const loginResponse = await wxLogin(loginParams)

    // 构造用户信息对象
    const userInfo: UserInfo = {
      id: '', // 临时ID，后续通过获取用户信息接口补充
      openid: '',
      nickname: '',
      avatar: '',
      status: loginResponse.userStatus,
      token: loginResponse.token
    }

    // 保存基本用户信息到Store
    setProfile(userInfo)

    // 获取完整的用户详细信息
    try {
      console.log('开始获取用户详细信息...');
      const detailedUserInfo = await getUserInfo();
      console.log('获取到的详细用户信息:', detailedUserInfo);

      // 更新完整的用户信息
      userStore.updateProfile(detailedUserInfo);
    } catch (error) {
      console.error('获取用户详细信息失败:', error);
      // 获取详细信息失败不影响登录流程，继续执行
    }

    // 登录成功提示
    showToast('登录成功', 'success')

    // 根据用户状态进行页面跳转
    setTimeout(() => {
      navigateByUserStatus(loginResponse.userStatus)
    }, 1500)

  } catch (error) {
    console.error('微信登录失败:', error)
    showToast('登录失败，请重试', 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * 根据用户状态进行页面跳转
 * @param status 用户状态
 */
function navigateByUserStatus(status: UserInfo['status']): void {
  switch (status) {
    case 'approved':
      // 已审核通过的正式用户，跳转到信息中心
      uni.reLaunch({ url: '/pages/info/info' })
      break
    case 'pending_review':
      // 待审核用户，跳转到个人中心查看审核状态
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'rejected':
      // 审核未通过用户，跳转到个人中心修改资料
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'new':
    default:
      // 未提交资料的新用户，跳转到注册页面
      uni.navigateTo({ url: '/pages/register/register' })
      break
  }
}
</script>

<style lang="scss" scoped>
/*
  疾控考试系统登录页面样式
  设计主题：医疗健康风格 - 浅蓝+白色，绿蓝渐变
  复选框修复版本：2025-06-17T23:48:55
*/

/* ==================== 页面基础设置 ==================== */
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 50%, #e8f5e8 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* ==================== 背景装饰 ==================== */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));

  &--1 {
    width: 320rpx;
    height: 320rpx;
    top: -160rpx;
    right: -160rpx;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.08), rgba(33, 150, 243, 0.08));
  }

  &--2 {
    width: 240rpx;
    height: 240rpx;
    bottom: 20%;
    left: -120rpx;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.06), rgba(76, 175, 80, 0.06));
  }

  &--3 {
    width: 180rpx;
    height: 180rpx;
    top: 30%;
    left: 20%;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.04), rgba(33, 150, 243, 0.04));
  }
}

/* ==================== 主容器 ==================== */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  padding: 80rpx 60rpx 60rpx;
}

/* ==================== 品牌区域 ==================== */
.brand-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}

.logo-wrapper {
  margin-bottom: 40rpx;
}

.logo-background {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #4CAF50, #2196F3);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8rpx 24rpx rgba(76, 175, 80, 0.2),
    0 4rpx 12rpx rgba(33, 150, 243, 0.15);
}

.brand-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.main-title {
  font-size: 40rpx;
  color: #2c5aa0;
  font-weight: 600;
  line-height: 1.2;
}

.sub-title {
  font-size: 28rpx;
  color: #5a7ba8;
  line-height: 1.4;
}

/* ==================== 登录区域 ==================== */
.login-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* 确保按钮位于60-70%高度 */
  margin-top: auto;
  margin-bottom: auto;
  transform: translateY(-10%);
}

.login-button-wrapper {
  width: 100%;
  margin-bottom: 48rpx;
}



/* ==================== 协议区域 ==================== */
.agreement-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.agreement-links {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.agreement-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.agreement-link {
  font-size: 26rpx;
  color: #4CAF50;
  line-height: 1.4;
  text-decoration: underline;
  cursor: pointer;
}

/* ==================== 模态框内容 ==================== */
.modal-content {
  padding: 32rpx 24rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.modal-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  white-space: pre-line;
}

/* ==================== uview-plus组件样式定制 ==================== */
/* 复选框样式优化 - 清晰的视觉反馈 */
:deep(.u-checkbox__icon-wrap) {
  border: 2rpx solid #e0e0e0 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
}

:deep(.u-checkbox--checked .u-checkbox__icon-wrap) {
  box-shadow: 0 2rpx 12rpx rgba(76, 175, 80, 0.3) !important;
}

/* 协议链接点击效果 */
:deep(.u-text) {
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
  .main-container {
    padding: 60rpx 40rpx 40rpx;
  }

  .logo-background {
    width: 100rpx;
    height: 100rpx;
  }

  .brand-section {
    margin-bottom: 100rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .main-container {
    padding: 40rpx 32rpx 32rpx;
  }

  .logo-background {
    width: 80rpx;
    height: 80rpx;
  }

  .brand-section {
    margin-bottom: 80rpx;
  }

  .login-section {
    transform: translateY(-5%);
  }
}
</style>

<!-- 新增：全局page高度修正，确保小程序下全屏 -->
<style>
page {
  height: 100%;
}
</style>
